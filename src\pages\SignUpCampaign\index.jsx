import { useState, useEffect } from 'react';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import { useGoogleLogin } from '@react-oauth/google';
import { Helmet } from 'react-helmet-async';
import signupGoogleUseCase from '@/application/auth/signupGoogle';
import { useUserStore } from '@/stores/user/userStore';
import DLoading from '@/components/DLoading';
import DFullLogo from '@/components/Global/DLogo/DFullLogo';
import LayoutPublicNoPadding from './LayoutPublicNoPadding';

// Import our components
import CreateAccount from '../SignUp/steps/CreateAccount';
import VerifyEmail from '../SignUp/steps/VerifyEmail';

// Define our signup steps
export const SignUpSteps = {
  CREATE_ACCOUNT: 'create_account',
  VERIFY_EMAIL: 'verify_email',
};

const SignUpCampaign = () => {
  const [urlParams] = useSearchParams();
  const navigate = useNavigate();
  const user = useUserStore((state) => state.user);

  // State for managing the flow
  const [currentStep, setCurrentStep] = useState(SignUpSteps.CREATE_ACCOUNT);
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [contactConsent, setContactConsent] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Page loading state
  const [pageLoading, setPageLoading] = useState(true);

  /**
   * Handles Google OAuth signup
   */
  const handleGoogleLogIn = useGoogleLogin({
    onSuccess: async (tokenResponse) => {
      setLoading(true);
      setError('');
      try {
        const rewardful_referral = window.Rewardful?.referral;
        const response = await signupGoogleUseCase({
          ...tokenResponse,
          rewardful_referral,
          contact_consent: contactConsent,
          isLanding: urlParams.get('is_landing_page'),
          kb_id: urlParams.get('kb_id'),
        });

        if (response.status === 200) {
          navigate('/');
        }
      } catch (err) {
        console.error(err);
        // Don't set a custom error message here - let the axios interceptor handle it
        // The backend error (like "user already exists") will be shown via toast notification
      } finally {
        setLoading(false);
      }
    },
    onError: (error) => {
      console.error('Login Failed:', error);
      setError('Google sign up failed. Please try again.');
      setLoading(false);
    },
  });

  useEffect(() => {
    // Check if user is already logged in
    if (user?.id) {
      navigate('/');
      return;
    }

    // Set page as loaded
    setPageLoading(false);
  }, [user, navigate]);

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case SignUpSteps.CREATE_ACCOUNT:
        return (
          <CreateAccount
            email={email}
            setEmail={setEmail}
            contactConsent={contactConsent}
            setContactConsent={setContactConsent}
            handleGoogleLogIn={handleGoogleLogIn}
            goToNextStep={() => setCurrentStep(SignUpSteps.VERIFY_EMAIL)}
            loading={loading}
            setLoading={setLoading}
            error={error}
            setError={setError}
          />
        );
      case SignUpSteps.VERIFY_EMAIL:
        return (
          <VerifyEmail
            email={email}
            code={code}
            setCode={setCode}
            goToNextStep={() => navigate('/')}
            goBack={() => setCurrentStep(SignUpSteps.CREATE_ACCOUNT)}
            loading={loading}
            setLoading={setLoading}
            error={error}
            setError={setError}
            contactConsent={contactConsent}
          />
        );
      default:
        return (
          <CreateAccount
            email={email}
            setEmail={setEmail}
            contactConsent={contactConsent}
            setContactConsent={setContactConsent}
            handleGoogleLogIn={handleGoogleLogIn}
            goToNextStep={() => setCurrentStep(SignUpSteps.VERIFY_EMAIL)}
            loading={loading}
            setLoading={setLoading}
            error={error}
            setError={setError}
          />
        );
    }
  };

  if (pageLoading) {
    return <DLoading show={true} />;
  }

  // Left side content with signup form
  const leftSideContent = (
    <div className="flex flex-col items-center justify-center w-full h-full">
      <div className="w-full max-w-md animate-fadeIn">
        {/* Logo on top of the form */}
        <div className="flex justify-center mb-8">
          <DFullLogo />
        </div>

        <div className="animate-fadeInUp">
          {renderStep()}

          {/* Login Link - always shown */}
          <div className="bg-gray-100 py-4 rounded-b-xl md:rounded-b-3xl text-center shadow-lg">
            <p className="text-sm text-gray-500">
              Already have an account?{' '}
              <Link to="/log-in" className="text-indigo-600 hover:underline">
                Log in
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  // Right side content with image placeholder and black background
  const rightSideContent = (
    <div className="h-full w-full flex items-center justify-center bg-[#09090a] p-0 m-0 overflow-hidden">
      {/* Image container that fills the entire space with 90% scale */}
      <div className="w-full h-full scale-90 flex items-center justify-center">
        <a href="https://www.trustpilot.com/review/dante-ai.com" target="_blank" rel="noopener noreferrer">
          <img
            src="https://dante-public-files.lon1.cdn.digitaloceanspaces.com/new-signup-bg.png"
            alt="Sign up campaign"
            className="w-full h-full object-contain cursor-pointer"
          />
        </a>
      </div>
    </div>
  );

  return (
    <>
      <Helmet>
        <link
          rel="preload"
          href="https://dante-public-files.lon1.cdn.digitaloceanspaces.com/new-signup-bg.png"
          as="image"
        />
      </Helmet>
      <LayoutPublicNoPadding
        leftSide={leftSideContent}
        rightSide={rightSideContent}
      />
    </>
  );
};

export default SignUpCampaign;
