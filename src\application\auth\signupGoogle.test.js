import { describe, expect, it, vi } from 'vitest';

import * as authService from '@/services/auth.service';
import { useUserStore } from '@/stores/user/userStore';

import getUserInfo from '../user/getUserInfo';
import { updateUserDataLayer, trackSignup } from '@/helpers/analytics';

import signupGoogleUseCase from './signupGoogle';

// Mock functions in useUserStore, authService, getUserInfo, and analytics
vi.mock('@/stores/user/userStore', () => ({
  useUserStore: {
    getState: vi.fn()
  }
}));

vi.mock('@/services/auth.service', () => ({
  signupWithGoogle: vi.fn()
}));

vi.mock('../user/getUserInfo', () => ({
  default: vi.fn()
}));

vi.mock('@/helpers/analytics', () => ({
  updateUserDataLayer: vi.fn(),
  trackSignup: vi.fn()
}));

describe('signupGoogleUseCase', () => {
  it('should save auth details and user info on successful Google signup', async () => {
    const mockSaveAuthDetail = vi.fn();
    const mockSetUser = vi.fn();

    // Mock user store functions
    useUserStore.getState.mockReturnValue({
      saveAuthDetail: mockSaveAuthDetail,
      setUser: mockSetUser
    });

    // Mock the response from authService and getUserInfo
    authService.signupWithGoogle.mockResolvedValue({
      status: 200,
      data: { access_token: 'test_access_token' }
    });

    const mockUserInfo = {
      id: 1,
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      date_created: '2023-01-01',
      login_count: 1
    };
    getUserInfo.mockResolvedValue(mockUserInfo);

    const result = await signupGoogleUseCase({
      access_token: 'google_access_token',
      rewardful_referral: 'referral_code',
      contact_consent: true,
      kb_id: 'kb123',
      isLanding: true,
      trial_plan_type: 'pro'
    });

    expect(mockSaveAuthDetail).toHaveBeenCalledWith({
      access_token: 'test_access_token',
      user_id: mockUserInfo.id,
      first_name: mockUserInfo.first_name,
      last_name: mockUserInfo.last_name,
      email: mockUserInfo.email,
      date_created: mockUserInfo.date_created,
      login_count: mockUserInfo.login_count
    });
    expect(mockSetUser).toHaveBeenCalledWith(mockUserInfo);
    expect(trackSignup).toHaveBeenCalledWith({
      user_id: mockUserInfo.id,
      email: mockUserInfo.email,
      first_name: mockUserInfo.first_name,
      last_name: mockUserInfo.last_name,
      tier_name: 'pro',
      billing_cycle: 'none',
      signup_method: 'Google'
    });
    expect(updateUserDataLayer).toHaveBeenCalledWith(mockUserInfo, expect.any(Object));
    expect(result.status).toBe(200);
    expect(result.data).toEqual({ access_token: 'test_access_token', ...mockUserInfo });
  });

  it('should return an empty object if the response status is not 200', async () => {
    authService.signupWithGoogle.mockResolvedValue({
      status: 400,
      data: {}
    });

    const result = await signupGoogleUseCase({
      access_token: 'google_access_token',
      rewardful_referral: 'referral_code',
      contact_consent: true,
      kb_id: 'kb123',
      isLanding: true
    });

    expect(result).toEqual({});
  });

  it('should throw error when user already exists (let axios interceptor handle it)', async () => {
    const error = new Error('User already exists');
    error.response = {
      status: 400,
      data: { detail: 'REGISTER_USER_ALREADY_EXISTS' }
    };
    authService.signupWithGoogle.mockRejectedValue(error);

    // The function should now throw the error instead of catching it
    // This allows the axios interceptor to handle it and show the proper toast
    await expect(signupGoogleUseCase({
      access_token: 'google_access_token',
      rewardful_referral: 'referral_code',
      contact_consent: true,
      kb_id: 'kb123',
      isLanding: true
    })).rejects.toThrow('User already exists');
  });

  it('should throw any API error (let axios interceptor handle it)', async () => {
    const error = new Error('Network error');
    authService.signupWithGoogle.mockRejectedValue(error);

    // The function should now throw the error instead of catching it
    await expect(signupGoogleUseCase({
      access_token: 'google_access_token',
      rewardful_referral: 'referral_code',
      contact_consent: true,
      kb_id: 'kb123',
      isLanding: true
    })).rejects.toThrow('Network error');
  });
});
